
import React, { useEffect, useState } from 'react';
import { Play, Wand2, Upload, Settings, Image, Video, ChevronDown } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import {
  api,
  queryKeys,
  mutationKeys,
  CreateVideoRequest,
  CreateImageRequest,
  CreateVideoResponse,
  CreateImageResponse,
  GenerationRequestData,
  GenerationRequestStatus,
  OutputResultFile,
  VideoModel,
  ImageModel,
  TokenEstimationResponse,
} from '@/lib/api';
import { useQuery, useMutation } from '@tanstack/react-query';
import { toast } from '@/components/ui/use-toast';
import { usePBContext } from '@/context/PocketbaseContext';
import { useTranslation } from 'react-i18next';
import { getModelConfig, getParametersByGroup, getGroupConfigs, getDefaultValues } from '@/lib/models';


const VideoGenerator = () => {
  const { pb, user } = usePBContext();
  const { t } = useTranslation();
  const [prompt, setPrompt] = useState('');
  const [translatePrompt, setTranslatePrompt] = useState(true);
  const [activeTab, setActiveTab] = useState<'video' | 'image'>('image');
  const [selectedModel, setSelectedModel] = useState('imagen-4-ultra');

  // Demo images mapping for image models
  const modelDemoImages: Record<string, string> = {
    // 'flux-dev': '/model-demos/white-haired-woman/flux-dev.webp',
    'flux-kontext-pro': '/model-demos/white-haired-woman/flux-kontext-pro.png',
    'imagen-4-ultra': '/model-demos/white-haired-woman/imagen-4-ultra.jpg',
    'seedream-3': '/model-demos/white-haired-woman/seedream-3.jpg',
  };

  // State for tracking active generation requests
  const [activeGeneration, setActiveGeneration] = useState<GenerationRequestData | undefined>(undefined);

  // Model-specific parameters from mapping (used for both video and image)
  const [modelParameters, setModelParameters] = useState<Record<string, any>>({});

  // Track which parameter groups are expanded
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  // Token estimation state
  const [estimatedTokens, setEstimatedTokens] = useState<number>(0);


  const uploadImageMutation = useMutation({
    mutationKey: mutationKeys.uploadImage,
    mutationFn: (file: File) => api.uploadImage(pb, file),
    onError: (error) => {
      console.error('Upload image error:', error);
      toast({
        title: t('videoGenerator.toastMessages.error'),
        description: t('videoGenerator.toastMessages.error',{error: error}),
        variant: 'destructive',
      });
    },
  });

  // Helper function to translate option labels
  const getTranslatedOptionLabel = (paramName: string, optionValue: string, fallbackLabel: string) => {
    // Try specific parameter option first
    const specificKey = `videoGenerator.parameters.options.${paramName}.${optionValue}`;
    const specificTranslation = t(specificKey, { defaultValue: null });
    if (specificTranslation !== specificKey) {
      return specificTranslation;
    }

    // Try general option categories
    const generalCategories = [
      'aspectRatios', 'videoLength', 'duration', 'resolution',
      'imageCount', 'formats', 'megapixels', 'safetyFilterLevel', 'imageSize'
    ];

    for (const category of generalCategories) {
      const categoryKey = `videoGenerator.options.${category}.${optionValue}`;
      const categoryTranslation = t(categoryKey, { defaultValue: null });
      if (categoryTranslation !== categoryKey) {
        return categoryTranslation;
      }
    }

    // Fallback to original label
    return fallbackLabel;
  };

  // Parameter renderer using UI components
  const renderParameter = (paramName: string, config: any, value: any, onChange: (val: any) => void) => {
    switch (config.type) {
      case 'textarea':
        return (
          <Textarea
            value={value || config.defaultValue || ''}
            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(e.target.value)}
            placeholder={config.placeholder}
            className="min-h-[120px]"
          />
        );
      case 'select':
        return (
          <Select
            value={String(value || config.defaultValue || '')}
            onValueChange={(val: string) => onChange(val)}
          >
            <SelectTrigger>
              <SelectValue placeholder={String(t(`videoGenerator.parameters.placeholders.${paramName}`, config.placeholder || 'Select an option'))} />
            </SelectTrigger>
            <SelectContent>
              {config.options?.map((option: any) => (
                <SelectItem key={String(option.value)} value={String(option.value)}>
                  {getTranslatedOptionLabel(paramName, String(option.value), option.label)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
      case 'number':
        return (
          <Input
            type="number"
            value={value || config.defaultValue || ''}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(Number(e.target.value))}
            min={config.min}
            max={config.max}
            step={config.step}
            placeholder={String(t(`videoGenerator.parameters.placeholders.${paramName}`, config.placeholder))}
          />
        );
      case 'slider':
        return (
          <div className="space-y-2">
            <Slider
              value={[value || config.defaultValue || config.min || 0]}
              onValueChange={(vals: number[]) => onChange(vals[0])}
              min={config.min || 0}
              max={config.max || 100}
              step={config.step || 1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>{config.min || 0}</span>
              <span className="font-medium">{value || config.defaultValue || config.min || 0}</span>
              <span>{config.max || 100}</span>
            </div>
          </div>
        );
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              checked={value || config.defaultValue || false}
              onCheckedChange={(checked: boolean) => onChange(checked)}
            />
            <Label className="text-sm">
              {value || config.defaultValue ? t('videoGenerator.options.boolean.enabled') : t('videoGenerator.options.boolean.disabled')}
            </Label>
          </div>
        );
      case 'file':
        return (
          <div className="space-y-2">
            <Input
              type="file"
              accept={config.accept}
              onChange={async (e: React.ChangeEvent<HTMLInputElement>) => {
                const file = e.target.files?.[0];
                if (file) {
                  const res = await uploadImageMutation.mutateAsync(file)
                  if (res != '') {
                    onChange(res);
                  } else {
                    toast({
                      title: t('videoGenerator.toastMessages.error'),
                      description: t('videoGenerator.toastMessages.imageUploadFailedUnknownError'),
                      variant: 'destructive',
                    });
                  }
                }
              }}
            />
            {value && (
              <div className="text-sm text-muted-foreground">
                {t('videoGenerator.options.fileInput.uploaded')}
              </div>
            )}
          </div>
        );
      default:
        return (
          <Input
            type="text"
            value={value || config.defaultValue || ''}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
            placeholder={String(t(`videoGenerator.parameters.placeholders.${paramName}`, config.placeholder))}
          />
        );
    }
  };

  // Initialize model parameters and expanded groups when model changes
  useEffect(() => {
    console.log('debug selected model: ', selectedModel)
    const defaults = getDefaultValues(selectedModel);
    console.log('debug defaults model: ', defaults)
    setModelParameters(prev => ({
      prompt: prev.prompt || prompt, // Keep existing prompt
      ...defaults
    }));

    handleTokenEstimation(selectedModel, defaults);

    // Initialize expanded groups based on model configuration
    const modelConfig = getModelConfig(selectedModel);
    if (modelConfig?.groups) {
      const initialExpanded: Record<string, boolean> = {};
      Object.values(modelConfig.groups).forEach((group: any) => {
        initialExpanded[group.name] = group.defaultExpanded !== false; // Default to true unless explicitly false
      });
      setExpandedGroups(initialExpanded);
    }
  }, [selectedModel]);

  // Update prompt in model parameters when prompt changes
  useEffect(() => {
    setModelParameters(prev => ({
      ...prev,
      prompt: prompt
    }));
  }, [prompt]);

  useEffect(() => {
    handleTokenEstimation(selectedModel, modelParameters);
  }, [modelParameters.duration, modelParameters.resolution]);

  // Reset selected model when switching tabs
  const handleTabChange = (tab: 'video' | 'image') => {
    setActiveTab(tab);
    // Set default model for each tab
    if (tab === 'video') {
      setSelectedModel('seedance-pro');
    } else {
      setSelectedModel('imagen-4-ultra');
    }
  };

  // Function to estimate tokens for current configuration
  const handleTokenEstimation = (currentModel: string, params: Record<string, any>) => {
    if (activeTab === 'video') {
      const videoRequest: CreateVideoRequest = {
        model: currentModel,
        prompt: "",
        ...params,
        duration: params.duration ? Number(params.duration) : 0,
      };
      console.log('Video request: ', videoRequest)
      estimateVideoTokens(videoRequest);
    } else {
      const imageRequest: CreateImageRequest = {
        model: currentModel,
        prompt: "",
        ...params
      };
      console.log('Image request: ', imageRequest)
      estimateImageTokens(imageRequest);
    }
  };

  // // Subscribe to realtime updates for generation requests
  // useEffect(() => {
  //   if (!pb || !activeGeneration) return;
  //
  //   let subscription: (() => void | undefined) = undefined;
  //
  //   pb.collection('generation_requests').subscribe(activeGeneration.id, (e: any) => {
  //     console.log('Received generation update:', e);
  //
  //     if (e.action === 'update') {
  //       const updatedGeneration = e.record as GenerationRequestData;
  //       setActiveGeneration(updatedGeneration);
  //       // If generation is completed or failed, show toast
  //       if (updatedGeneration.status === 'completed') {
  //         toast({
  //           title: t('videoGenerator.toastMessages.success'),
  //           description: t('videoGenerator.toastMessages.videoGeneratedSuccess', { type: updatedGeneration.type === 'video' ? t('videoGenerator.tabs.video') : t('videoGenerator.tabs.image') }),
  //           variant: 'default',
  //         });
  //       } else if (updatedGeneration.status === 'failed') {
  //         toast({
  //           title: t('videoGenerator.toastMessages.error'),
  //           description: t('videoGenerator.toastMessages.generationFailedDescription', {
  //             type: updatedGeneration.type === 'video' ? t('videoGenerator.tabs.video') : t('videoGenerator.tabs.image'),
  //             error: updatedGeneration.error
  //           }),
  //           variant: 'destructive',
  //         });
  //       }
  //     }
  //   }, { expand: 'output_files' }).then((unsubscribe: () => void) => {
  //     subscription = unsubscribe;
  //   }).catch((error: any) => {
  //     console.error('Failed to subscribe to generation updates:', error);
  //   });
  //
  //   // Cleanup subscriptions
  //   return () => {
  //     if (subscription) {
  //       subscription();
  //     }
  //   };
  // }, [pb, activeGeneration]);


  const {data: longPolledActive} = useQuery({
    queryKey: ['longpoll-active',activeGeneration?.id],
    enabled: !!activeGeneration && (activeGeneration?.status != 'failed' && activeGeneration?.status != 'completed'),
    queryFn: () => api.getGenerationRequest(pb, activeGeneration.id),
    refetchInterval: 2000,
    staleTime: 0,
  })
  useEffect(() => {
    if (longPolledActive && longPolledActive.id == activeGeneration?.id) {
      setActiveGeneration(longPolledActive);
      // If generation is completed or failed, show toast
      if (longPolledActive.status === 'completed') {
        toast({
          title: t('videoGenerator.toastMessages.success'),
          description: t('videoGenerator.toastMessages.videoGeneratedSuccess', { type: longPolledActive.type === 'video' ? t('videoGenerator.tabs.video') : t('videoGenerator.tabs.image') }),
          variant: 'default',
        });
      } else if (longPolledActive.status === 'failed') {
        toast({
          title: t('videoGenerator.toastMessages.error'),
          description: t('videoGenerator.toastMessages.generationFailedDescription', {
            type: longPolledActive.type === 'video' ? t('videoGenerator.tabs.video') : t('videoGenerator.tabs.image'),
            error: longPolledActive.error
          }),
          variant: 'destructive',
        });
      }
    }
  }, [longPolledActive]);

  // Fetch models based on active tab
  const {
    data: models = [],
    isLoading: isLoadingModels
  } = useQuery({
    queryKey: queryKeys.models(activeTab),
    queryFn: () => api.getModels(activeTab)
  });

  // Video generation mutation
  const {
    mutate: generateVideo,
    isPending: isGeneratingVideo,
  } = useMutation({
    mutationKey: mutationKeys.createVideo,
    mutationFn: (request: CreateVideoRequest) => api.createVideo(pb, request),
    onSuccess: (response: CreateVideoResponse) => {
      // Add the new generation request to active generations
      const generationData: GenerationRequestData = {
        id: response.request_id,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        user: '', // Will be filled by backend
        error: '',
        model: selectedModel,
        type: 'video',
        replicate_id: response.prediction_id,
        status: response.status as GenerationRequestStatus,
        jsondata: {
          type: 'video',
          prompt: prompt,
          input: null,
          output: null
        },
        output_files: [],
        expand: null
      };

      setActiveGeneration(generationData);

      toast({
        title: t('videoGenerator.toastMessages.videoGenerationStarted'),
        description: t('videoGenerator.toastMessages.generationStartedDescription', { type: t('videoGenerator.types.video') }),
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      console.log('Generation start error', error);
      toast({
        title: t('videoGenerator.toastMessages.error'),
        description: t('videoGenerator.toastMessages.failedToStart', { type: t('videoGenerator.types.video') }),
        variant: 'destructive',
      });
    }
  });

  // Image generation mutation
  const {
    mutate: generateImage,
    isPending: isGeneratingImage,
  } = useMutation({
    mutationKey: mutationKeys.createImage,
    mutationFn: (request: CreateImageRequest) => api.createImage(pb, request),
    onSuccess: (response: CreateImageResponse) => {
      // Add the new generation request to active generations
      const generationData: GenerationRequestData = {
        id: response.request_id,
        created: new Date().toISOString(),
        updated: new Date().toISOString(),
        user: '', // Will be filled by backend,
        error: '',
        model: selectedModel,
        type: 'image',
        replicate_id: response.prediction_id,
        status: response.status as GenerationRequestStatus,
        jsondata: {
          type: 'image',
          prompt: prompt,
          input: null,
          output: null
        },
        output_files: [],
        expand: null
      };

      setActiveGeneration(generationData);

      toast({
        title: t('videoGenerator.toastMessages.imageGenerationStarted'),
        description: t('videoGenerator.toastMessages.generationStartedDescription', { type: t('videoGenerator.types.image') }),
        variant: 'default',
      });
    },
    onError: (error: Error) => {
      console.log('Generation start error', error);
      toast({
        title: t('videoGenerator.toastMessages.error'),
        description: t('videoGenerator.toastMessages.failedToStart', { type: t('videoGenerator.types.image') }),
        variant: 'destructive',
      });
    }
  });

  // Video token estimation mutation
  const {
    mutate: estimateVideoTokens,
    isPending: isEstimatingVideoTokens,
  } = useMutation({
    mutationKey: mutationKeys.estimateCreateVideo,
    mutationFn: (request: CreateVideoRequest) => api.estimateCreateVideo(pb, request),
    onSuccess: (response: TokenEstimationResponse) => {
      setEstimatedTokens(response.estimation);
    },
    onError: (error) => {
      console.error('Video token estimation error:', error);
    },
  });

  // Image token estimation mutation
  const {
    mutate: estimateImageTokens,
    isPending: isEstimatingImageTokens,
  } = useMutation({
    mutationKey: mutationKeys.estimateCreateImage,
    mutationFn: (request: CreateImageRequest) => api.estimateCreateImage(pb, request),
    onSuccess: (response: TokenEstimationResponse) => {
      setEstimatedTokens(response.estimation);
    },
    onError: (error) => {
      console.error('Image token estimation error:', error);
    },
  });

  const handleGenerate = () => {
    if (activeTab === 'video') {
      const videoRequest: CreateVideoRequest = {
        model: selectedModel as VideoModel,
        translate_prompt: translatePrompt,
        prompt,
        ...modelParameters // Use the dynamic model parameters
      };
      generateVideo(videoRequest);
    } else {
      const imageRequest: CreateImageRequest = {
        model: selectedModel as ImageModel,
        translate_prompt: translatePrompt,
        prompt,
        ...modelParameters // Use the dynamic model parameters
      };
      generateImage(imageRequest);
    }
  };



  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-3">{t('studio.title')}</h1>
        <p className="text-gray-600 text-lg">{t('studio.description')}</p>
      </div>

      {/* Tab Selection */}
      <div className="flex justify-center">
        <div className="bg-gray-100 rounded-xl p-1 inline-flex">
          <button
            onClick={() => handleTabChange('video')}
            className={`px-6 py-2 rounded-lg transition-colors flex items-center space-x-2 ${
              activeTab === 'video'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Video className="w-4 h-4" />
            <span>{t('videoGenerator.tabs.video')}</span>
          </button>
          <button
            onClick={() => handleTabChange('image')}
            className={`px-6 py-2 rounded-lg transition-colors flex items-center space-x-2 ${
              activeTab === 'image'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            <Image className="w-4 h-4" />
            <span>{t('videoGenerator.tabs.image')}</span>
          </button>
        </div>
      </div>

      {/* Generation Card */}
      <Card className="bg-white border border-gray-200 shadow-sm max-w-4xl mx-auto">
        <CardContent className="p-8">
          <div className="space-y-6">
            {/* Model Selection */}
            <div>
              <label className="block text-gray-900 font-medium mb-3 text-lg">
                {t('videoGenerator.selectModel')}
              </label>
              {isLoadingModels ? (
                <div className="flex justify-center py-8">
                  <div className="w-6 h-6 border-2 border-[var(--primary-blue)] border-t-transparent rounded-full animate-spin" />
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {models.map((model: any) => (
                    <Card
                      key={model.id}
                      className={`cursor-pointer transition-all overflow-hidden ${
                        selectedModel === model.id
                          ? 'border-[var(--primary-blue)] bg-blue-50 ring-2 ring-blue-200'
                          : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                      }`}
                      onClick={() => setSelectedModel(model.id)}
                    >
                      {/* Demo image for image models */}
                      {activeTab === 'image' && modelDemoImages[model.id] && (
                        <div className="aspect-[4/3] overflow-hidden">
                          <img
                            src={modelDemoImages[model.id]}
                            alt={`${model.name} demo`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      )}
                      <CardContent className="p-4">
                        <div className="font-medium text-gray-900 mb-1">
                          {String(t(`videoGenerator.models.${model.id}.name`, model.name))}
                        </div>
                        <div className="text-sm text-gray-600">
                          {String(t(`videoGenerator.models.${model.id}.description`, model.description))}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Prompt Input */}
            <div>
              <label className="block text-gray-900 font-medium mb-3 text-lg">
                {t('videoGenerator.promptLabel')}
              </label>
              <Textarea
                placeholder={t('videoGenerator.promptPlaceholder')}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="min-h-[120px] border-gray-200 focus:border-[var(--primary-blue)] focus:ring-[var(--primary-blue)] text-base"
              />

              {/* Translate Prompt Checkbox */}
              <div className="flex items-center space-x-2 mt-3">
                <Checkbox
                  id="translate-prompt"
                  checked={translatePrompt}
                  onCheckedChange={(checked) => setTranslatePrompt(checked as boolean)}
                />
                <Label htmlFor="translate-prompt" className="text-sm font-medium">
                  {t('videoGenerator.translatePrompt.label')}
                </Label>
              </div>
            </div>

            {/* Model-Specific Parameters */}
            <div className="space-y-6">

              {/* Video-Specific Parameters - Dynamic Model-Based Rendering */}
              {activeTab === 'video' && (() => {
                const modelConfig = getModelConfig(selectedModel);
                if (!modelConfig || modelConfig.metadata?.type !== 'video') {
                  return (
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <p className="text-gray-600">No configuration available for {selectedModel}</p>
                    </div>
                  );
                }

                const parametersByGroup = getParametersByGroup(selectedModel);
                const groupConfigs = getGroupConfigs(selectedModel);

                return (
                  <div className="space-y-4">
                    {groupConfigs.map(groupConfig => {
                      const groupParameters = parametersByGroup[groupConfig.name] || [];
                      if (groupParameters.length === 0) return null;

                      const isCollapsible = groupConfig.collapsible === true;
                      const isExpanded = expandedGroups[groupConfig.name] ?? true;

                      if (isCollapsible) {
                        return (
                          <Collapsible
                            key={groupConfig.name}
                            open={isExpanded}
                            onOpenChange={(open) =>
                              setExpandedGroups(prev => ({ ...prev, [groupConfig.name]: open }))
                            }
                          >
                            <Card className="border border-gray-200">
                              <CollapsibleTrigger asChild>
                                <Button
                                  variant="ghost"
                                  className="w-full justify-between p-4 h-auto font-medium text-left hover:bg-gray-50"
                                >
                                  <div>
                                    <h4 className="font-medium text-gray-900">{groupConfig.label}</h4>
                                    {groupConfig.description && (
                                      <p className="text-xs text-wrap text-gray-600 mt-1">{groupConfig.description}</p>
                                    )}
                                  </div>
                                  <ChevronDown
                                    className={`h-4 w-4 transition-transform duration-200 ${
                                      isExpanded ? 'transform rotate-180' : ''
                                    }`}
                                  />
                                </Button>
                              </CollapsibleTrigger>
                              <CollapsibleContent>
                                <div className="p-4 pt-0">
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    {groupParameters.map(({ name, config }: { name: string; config: any }) => (
                                      <div key={name} className="space-y-2">
                                        <Label htmlFor={name} className="text-sm font-medium">
                                          {String(t(`videoGenerator.parameters.labels.${name}`, config.label))}
                                          {config.required && <span className="text-destructive ml-1">*</span>}
                                        </Label>
                                        <div id={name}>
                                          {renderParameter(
                                            name,
                                            config,
                                            modelParameters[name],
                                            (value) => setModelParameters(prev => ({ ...prev, [name]: value }))
                                          )}
                                        </div>
                                        {config.description && (
                                          <p className="text-xs text-muted-foreground mt-1">
                                            {String(t(`videoGenerator.parameters.descriptions.${name}`, config.description))}
                                          </p>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </CollapsibleContent>
                            </Card>
                          </Collapsible>
                        );
                      } else {
                        // Non-collapsible group - always expanded
                        return (
                          <div key={groupConfig.name} className="p-4 bg-blue-50 rounded-lg space-y-4">
                            <div>
                              <h4 className="font-medium text-gray-900">{groupConfig.label}</h4>
                              {groupConfig.description && (
                                <p className="text-xs text-wrap text-gray-600 mt-1">{groupConfig.description}</p>
                              )}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              {groupParameters.map(({ name, config }: { name: string; config: any }) => (
                                <div key={name} className="space-y-2">
                                  <Label htmlFor={name} className="text-sm font-medium">
                                    {String(t(`videoGenerator.parameters.labels.${name}`, config.label))}
                                    {config.required && <span className="text-destructive ml-1">*</span>}
                                  </Label>
                                  <div id={name}>
                                    {renderParameter(
                                      name,
                                      config,
                                      modelParameters[name],
                                      (value) => setModelParameters(prev => ({ ...prev, [name]: value }))
                                    )}
                                  </div>
                                  {config.description && (
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {String(t(`videoGenerator.parameters.descriptions.${name}`, config.description))}
                                    </p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      }
                    })}
                  </div>
                );
              })()}

              {/* Image-Specific Parameters - Dynamic Model-Based Rendering */}
              {activeTab === 'image' && (() => {
                const modelConfig = getModelConfig(selectedModel);
                if (!modelConfig || modelConfig.metadata?.type !== 'image') {
                  return (
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <p className="text-gray-600">No configuration available for {selectedModel}</p>
                    </div>
                  );
                }

                const parametersByGroup = getParametersByGroup(selectedModel);
                const groupConfigs = getGroupConfigs(selectedModel);

                return (
                  <div className="space-y-4">
                    {groupConfigs.map((groupConfig: any) => {
                      const groupParameters = parametersByGroup[groupConfig.name] || [];
                      if (groupParameters.length === 0) return null;

                      const isCollapsible = groupConfig.collapsible === true;
                      const isExpanded = expandedGroups[groupConfig.name] ?? true;

                      if (isCollapsible) {
                        return (
                          <Collapsible
                            key={groupConfig.name}
                            open={isExpanded}
                            onOpenChange={(open: boolean) =>
                              setExpandedGroups(prev => ({ ...prev, [groupConfig.name]: open }))
                            }
                          >
                            <Card className="border border-gray-200">
                              <CollapsibleTrigger asChild>
                                <Button
                                  variant="ghost"
                                  className="w-full justify-between p-4 h-auto font-medium text-left hover:bg-gray-50"
                                >
                                  <div>
                                    <h4 className="font-medium text-gray-900">{groupConfig.label}</h4>
                                    {groupConfig.description && (
                                      <p className="text-xs text-wrap text-gray-600 mt-1">{groupConfig.description}</p>
                                    )}
                                  </div>
                                  <ChevronDown
                                    className={`h-4 w-4 transition-transform duration-200 ${
                                      isExpanded ? 'transform rotate-180' : ''
                                    }`}
                                  />
                                </Button>
                              </CollapsibleTrigger>
                              <CollapsibleContent>
                                <div className="p-4 pt-0">
                                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    {groupParameters.map(({ name, config }: { name: string; config: any }) => (
                                      <div key={name} className="space-y-2">
                                        <Label htmlFor={name} className="text-sm font-medium">
                                          {config.label}
                                          {config.required && <span className="text-destructive ml-1">*</span>}
                                        </Label>
                                        <div id={name}>
                                          {renderParameter(
                                            name,
                                            config,
                                            modelParameters[name],
                                            (value) => setModelParameters(prev => ({ ...prev, [name]: value }))
                                          )}
                                        </div>
                                        {config.description && (
                                          <p className="text-xs text-muted-foreground mt-1">{config.description}</p>
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </CollapsibleContent>
                            </Card>
                          </Collapsible>
                        );
                      } else {
                        // Non-collapsible group - always expanded
                        return (
                          <div key={groupConfig.name} className="p-4 bg-green-50 rounded-lg space-y-4">
                            <div>
                              <h4 className="font-medium text-gray-900">{groupConfig.label}</h4>
                              {groupConfig.description && (
                                <p className="text-xs text-wrap text-gray-600 mt-1">{groupConfig.description}</p>
                              )}
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              {groupParameters.map(({ name, config }: { name: string; config: any }) => (
                                <div key={name} className="space-y-2">
                                  <Label htmlFor={name} className="text-sm font-medium">
                                    {config.label}
                                    {config.required && <span className="text-destructive ml-1">*</span>}
                                  </Label>
                                  <div id={name}>
                                    {renderParameter(
                                      name,
                                      config,
                                      modelParameters[name],
                                      (value) => setModelParameters(prev => ({ ...prev, [name]: value }))
                                    )}
                                  </div>
                                  {config.description && (
                                    <p className="text-xs text-muted-foreground mt-1">{config.description}</p>
                                  )}
                                </div>
                              ))}
                            </div>
                          </div>
                        );
                      }
                    })}
                  </div>
                );
              })()}
            </div>

            {/* Generate Button */}
            <Button
              onClick={handleGenerate}
              disabled={
                !prompt?.trim() ||
                isGeneratingVideo ||
                isGeneratingImage ||
                uploadImageMutation.isPending ||
                (estimatedTokens > 0 && (user?.tokens || 0) < estimatedTokens)
              }
              className={`w-full font-medium py-4 text-xs text-wrap rounded-lg ${
                estimatedTokens > 0 && (user?.tokens || 0) < estimatedTokens
                  ? 'bg-gray-400 hover:bg-gray-400 cursor-not-allowed text-white'
                  : 'bg-black hover:bg-gray-800 text-white'
              }`}
            >
              {(isGeneratingVideo || isGeneratingImage) ? (
                <>
                  <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  {t('videoGenerator.statusMessages.generating', { type: isGeneratingVideo ? t('videoGenerator.types.video') : t('videoGenerator.types.image') })}
                </>
              ) : estimatedTokens > 0 && (user?.tokens || 0) < estimatedTokens ? (
                <>
                  <Wand2 className="w-5 h-5 mr-2" />
                  {t('videoGenerator.statusMessages.notEnoughTokens', {amount: estimatedTokens})}
                </>
              ) : (
                <>
                  <Wand2 className="w-5 h-5 mr-2" />
                    <>
                      {t('videoGenerator.generateButton')} {activeTab === 'video' ? t('videoGenerator.tabs.video') : t('videoGenerator.tabs.image')}
                      {t('videoGenerator.statusMessages.tokensAmount', {amount: estimatedTokens})}
                    </>
                </>
              )}
            </Button>

            {/* Active Generations */}
            {activeGeneration && (
              <div className="mt-6 space-y-4">
                <h3 className="text-lg font-medium text-gray-900">{t('videoGenerator.activeGenerations.title')}</h3>
                <div key={activeGeneration.id} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">
                            {activeGeneration.type === 'video' ? t('videoGenerator.activeGenerations.videoGeneration') : t('videoGenerator.activeGenerations.imageGeneration')}
                          </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        activeGeneration.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          activeGeneration.status === 'completed' ? 'bg-green-100 text-green-800' :
                            activeGeneration.status === 'failed' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                      }`}>
                            {activeGeneration.status}
                          </span>
                    </div>
                    {activeGeneration.status === 'pending' && (
                      <div className="w-4 h-4 border-2 border-[var(--primary-blue)] border-t-transparent rounded-full animate-spin"/>
                    )}
                  </div>

                  <p className="text-sm text-gray-600 mb-3 truncate">{activeGeneration.jsondata?.prompt}</p>

                  {activeGeneration.status === 'completed' && activeGeneration.output_files && activeGeneration.output_files.length > 0 && (
                    <div className="space-y-3">
                      {activeGeneration.type === 'video' ? (
                        <div className="aspect-video bg-black rounded-lg flex items-center justify-center text-white relative">
                          <video
                            src={pb.files.getURL(activeGeneration.expand?.output_files[0], activeGeneration.expand?.output_files[0].file)}
                            controls
                            className="w-full h-full rounded-lg"
                          >
                            {t('videoGenerator.statusMessages.browserNotSupported')}
                          </video>
                        </div>
                      ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {activeGeneration.expand?.output_files.map((outputFile: any, index: number) => (
                            <div key={outputFile.id} className="relative">
                              <img
                                src={pb.files.getURL(outputFile, outputFile.file)}
                                alt={`${t('videoGenerator.activeGenerations.generatedImageAlt')} ${index + 1}`}
                                className="w-full h-auto rounded-lg border border-gray-200"
                              />

                              <Button
                                key={outputFile.id}
                                size="sm"
                                variant="ghost"
                                onClick={()=> {
                                  const url = pb.files.getURL(outputFile, outputFile.file);
                                  const link = document.createElement('a');
                                  link.href = url;
                                  link.download = `generation-${activeGeneration.id}`;
                                  link.click();
                                }}
                                className="text-sm text-[var(--primary-blue)] hover:text-[var(--primary-blue-hover)] underline"
                              >
                                {t('videoGenerator.activeGenerations.download')} {activeGeneration.type === 'video' ? t('videoGenerator.tabs.video') : `${t('videoGenerator.tabs.image')} ${index + 1}`}
                              </Button>
                            </div>

                          ))}
                        </div>
                      )}
                    </div>
                  )}

                  {activeGeneration.status === 'pending' && (
                    <div className="text-center py-8">
                      <div className="text-sm text-gray-600">
                        {t('videoGenerator.statusMessages.generating', { type: activeGeneration.type === 'video' ? t('videoGenerator.types.video') : t('videoGenerator.types.image') })}
                      </div>
                    </div>
                  )}

                  {activeGeneration.status === 'failed' && (
                    <div className="text-center py-4">
                      <div className="text-sm text-red-600">
                        {t('videoGenerator.statusMessages.generationFailed', {error: activeGeneration.error})}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VideoGenerator;
