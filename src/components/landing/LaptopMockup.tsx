const LaptopMockup = () => {
  return (
    <div className="relative max-w-4xl mx-auto mt-16 animate-fade-in-up mb-[-100px]" style={{ animationDelay: '0.6s' }}>
      {/* Laptop Frame */}
      <div className="relative">
        {/* Laptop Base */}
        <div className="bg-gradient-to-b from-gray-300 to-gray-400 rounded-2xl p-4 shadow-2xl">
          {/* Screen */}
          <div className="bg-black rounded-lg overflow-hidden aspect-video relative">
            {/* Screen Bezel */}
            <div className="absolute inset-2 bg-gray-900 rounded-lg overflow-hidden">
              {/* Service Screenshot */}
              <img
                src="screenshot.jpeg"
                alt="Holst AI Service Interface"
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* Camera dot */}
            <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-600 rounded-full"></div>
          </div>
        </div>
        
        {/* Laptop Bottom */}
        <div className="bg-gradient-to-b from-gray-400 to-gray-500 h-4 rounded-b-2xl -mt-2 mx-4 relative">
          {/* Trackpad */}
          <div className="absolute top-1 left-1/2 transform -translate-x-1/2 w-16 h-2 bg-gray-300 rounded-sm"></div>
        </div>
      </div>
      
      {/* Glow Effect */}
      <div className="absolute inset-0 bg-gradient-to-t from-[var(--primary-blue)]/20 to-transparent rounded-2xl blur-xl -z-10"></div>
    </div>
  );
};

export default LaptopMockup;
