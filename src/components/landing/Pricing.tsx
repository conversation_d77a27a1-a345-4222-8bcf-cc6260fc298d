import {Zap, Star, Coins, Crown, Image, Video} from 'lucide-react';
import {useTranslation} from 'react-i18next';
import React from "react";

const Pricing = () => {
    const {t} = useTranslation();

    // Token pricing tiers
    const tokenPackages = [
        {
            name: t('pricing.tokenPackages.packages.small.name'),
            tokens: 100,
            price: t('pricing.tokenPackages.packages.small.price'),
            pricePerToken: t('pricing.tokenPackages.packages.small.pricePerToken'),
            description: t('pricing.tokenPackages.packages.small.description'),
            icon: Coins,
            popular: false,
            savings: null,
            buttonText: t('pricing.tokenPackages.packages.small.buttonText')
        },
        {
            name: t('pricing.tokenPackages.packages.medium.name'),
            tokens: 600,
            price: t('pricing.tokenPackages.packages.medium.price'),
            pricePerToken: t('pricing.tokenPackages.packages.medium.pricePerToken'),
            description: t('pricing.tokenPackages.packages.medium.description'),
            icon: Zap,
            popular: true,
            savings: t('pricing.tokenPackages.packages.medium.savings'),
            buttonText: t('pricing.tokenPackages.packages.medium.buttonText')
        },
        {
            name: t('pricing.tokenPackages.packages.large.name'),
            tokens: 1000,
            price: t('pricing.tokenPackages.packages.large.price'),
            pricePerToken: t('pricing.tokenPackages.packages.large.pricePerToken'),
            description: t('pricing.tokenPackages.packages.large.description'),
            icon: Crown,
            popular: false,
            savings: t('pricing.tokenPackages.packages.large.savings'),
            buttonText: t('pricing.tokenPackages.packages.large.buttonText')
        }
    ];

  // Per-model token costs based on actual database prices
  const modelCosts = {
    image: [
      {
        name: t('pricing.modelCosts.imageModels.models.seedream3.name'),
        id: 'seedream-3',
        cost: t('pricing.modelCosts.imageModels.models.seedream3.cost'),
        description: t('pricing.modelCosts.imageModels.models.seedream3.description'),
        pricing: t('pricing.modelCosts.imageModels.models.seedream3.pricing')
      },
      {
        name: t('pricing.modelCosts.imageModels.models.imagen4Ultra.name'),
        id: 'imagen-4-ultra',
        cost: t('pricing.modelCosts.imageModels.models.imagen4Ultra.cost'),
        description: t('pricing.modelCosts.imageModels.models.imagen4Ultra.description'),
        pricing: t('pricing.modelCosts.imageModels.models.imagen4Ultra.pricing')
      },
      {
        name: t('pricing.modelCosts.imageModels.models.fluxKontextPro.name'),
        id: 'flux-kontext-pro',
        cost: t('pricing.modelCosts.imageModels.models.fluxKontextPro.cost'),
        description: t('pricing.modelCosts.imageModels.models.fluxKontextPro.description'),
        pricing: t('pricing.modelCosts.imageModels.models.fluxKontextPro.pricing')
      }
    ],
    video: [
      {
        name: t('pricing.modelCosts.videoModels.models.seedanceLite.name'),
        id: 'seedance-lite',
        variants: [
          { resolution: '480p', cost: 2, description: t('pricing.modelCosts.videoModels.models.seedanceLite.variants.480p') },
          { resolution: '1080p', cost: 4, description: t('pricing.modelCosts.videoModels.models.seedanceLite.variants.1080p') }
        ],
        description: t('pricing.modelCosts.videoModels.models.seedanceLite.description')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.seedancePro.name'),
        id: 'seedance-pro',
        variants: [
          { resolution: '480p', cost: 3, description: t('pricing.modelCosts.videoModels.models.seedancePro.variants.480p') },
          { resolution: '1080p', cost: 15, description: t('pricing.modelCosts.videoModels.models.seedancePro.variants.1080p') }
        ],
        description: t('pricing.modelCosts.videoModels.models.seedancePro.description')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.hunyuan.name'),
        id: 'hunyuan',
        cost: t('pricing.modelCosts.videoModels.models.hunyuan.cost'),
        description: t('pricing.modelCosts.videoModels.models.hunyuan.description'),
        pricing: t('pricing.modelCosts.videoModels.models.hunyuan.pricing')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.veo2.name'),
        id: 'veo2',
        cost: t('pricing.modelCosts.videoModels.models.veo2.cost'),
        description: t('pricing.modelCosts.videoModels.models.veo2.description'),
        pricing: t('pricing.modelCosts.videoModels.models.veo2.pricing')
      },
      {
        name: t('pricing.modelCosts.videoModels.models.veo3.name'),
        id: 'veo3',
        cost: t('pricing.modelCosts.videoModels.models.veo3.cost'),
        description: t('pricing.modelCosts.videoModels.models.veo3.description'),
        pricing: t('pricing.modelCosts.videoModels.models.veo3.pricing')
      }
    ]
  };

    return (
        <section id="pricing" className="py-20 relative">
            <div className="container mx-auto px-4" >
                <div className="text-center mb-16">
                    <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white">
                        {t('pricing.title')}{' '}
                        <span className="text-gradient">{t('pricing.titleHighlight')}</span>
                    </h1>
                    <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
                        {t('pricing.subtitle')}
                    </p>
                </div>

                {/* Token Packages */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-20">
                    {tokenPackages.map((pkg, index) => (
                        <div
                            key={index}
                            className={`relative glass-card rounded-2xl p-8 transition-all duration-300 hover:scale-105 animate-fade-in-up ${
                                pkg.popular
                                    ? 'ring-2 ring-[var(--primary-blue)] scale-105 animate-glow'
                                    : 'hover:shadow-2xl hover:shadow-blue-500/10'
                            }`}
                            style={{animationDelay: `${index * 0.1}s`}}
                        >
                            {pkg.popular && (
                                <div className="absolute -top-6 left-1/2 transform -translate-x-1/2">
                                    <div
                                        className="bg-[var(--primary-blue)] text-white px-2 py-2 rounded-full text-sm whitespace-nowrap font-semibold flex items-center gap-1">
                                        <Star className="w-4 h-4 fill-current"/>
                                        {t('pricing.popular')}
                                    </div>
                                </div>
                            )}

                            <div className="text-center mb-8">
                                <div className="flex justify-center mb-4">
                                    <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${
                                        pkg.popular
                                            ? 'bg-[var(--primary-blue)]'
                                            : 'bg-gray-700'
                                    }`}>
                                        <pkg.icon className="w-8 h-8 text-white"/>
                                    </div>
                                </div>
                                <h3 className="text-2xl font-bold text-white mb-2">{pkg.name}</h3>
                                <p className="text-gray-300 mb-4">{pkg.description}</p>
                                <div className="flex items-end justify-center mb-2">
                                    <span className="text-4xl font-bold text-white">{pkg.price}</span>
                                </div>
                                <div className="text-sm text-gray-400">
                                    {pkg.pricePerToken}
                                    {pkg.savings && (
                                        <span className="block text-green-400 font-semibold mt-1">
                        {pkg.savings}
                      </span>
                                    )}
                                </div>
                            </div>

                            {/*<Button*/}
                            {/*    className={`w-full py-3 rounded-full font-semibold transition-all duration-300 ${*/}
                            {/*        pkg.popular*/}
                            {/*            ? 'bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white hover:shadow-lg hover:shadow-blue-500/25'*/}
                            {/*            : 'bg-transparent border-2 border-[var(--primary-blue)] text-[var(--primary-blue)] hover:bg-[var(--primary-blue)] hover:text-white'*/}
                            {/*    }`}*/}
                            {/*>*/}
                            {/*    {pkg.buttonText}*/}
                            {/*</Button>*/}
                        </div>
                    ))}
                </div>

              {/* Model Pricing Section */}
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
                  {t('pricing.modelCosts.title')} <span className="text-gradient">{t('pricing.modelCosts.titleHighlight')}</span>
                </h2>
                <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                  {t('pricing.modelCosts.subtitle')}
                </p>
              </div>

              {/* Image Models */}
              <div className="mb-16">
                <div className="flex items-center justify-center mb-8">
                  <Image className="w-6 h-6 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-bold text-white">{t('pricing.modelCosts.imageModels.title')}</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                  {modelCosts.image.map((model, index) => (
                    <div
                      key={model.id}
                      className="glass-card rounded-xl p-6 text-center animate-fade-in-up"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <h4 className="text-lg font-semibold text-white mb-2">{model.name}</h4>
                      <div className="text-3xl font-bold text-[var(--primary-blue)] mb-2">
                        {model.cost}
                      </div>
                      <p className="text-sm text-gray-400 mb-2">{model.description}</p>
                      <p className="text-xs text-gray-500">{model.pricing}</p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Video Models */}
              <div className="mb-16">
                <div className="flex items-center justify-center mb-8">
                  <Video className="w-6 h-6 text-[var(--primary-blue)] mr-2" />
                  <h3 className="text-lg font-bold text-white">{t('pricing.modelCosts.videoModels.title')}</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                  {modelCosts.video.map((model, index) => (
                    <div
                      key={model.id}
                      className="glass-card rounded-xl p-6 animate-fade-in-up"
                      style={{ animationDelay: `${index * 0.1}s` }}
                    >
                      <div className="text-center mb-4">
                        <h4 className="text-lg font-semibold text-white mb-2">{model.name}</h4>
                        <p className="text-sm text-gray-400">{model.description}</p>
                      </div>

                      {model.variants ? (
                        // Models with resolution variants (Seedance)
                        <div className="space-y-3">
                          {model.variants.map((variant, variantIndex) => (
                            <div key={variantIndex} className="border border-gray-600 rounded-lg p-3">
                              <div className="flex justify-between items-center mb-1">
                                <span className="text-sm font-medium text-white">{variant.resolution}</span>
                                <span className="text-lg font-bold text-[var(--primary-blue)]">
                              {variant.cost}
                            </span>
                              </div>
                              <p className="text-xs text-gray-500">{variant.description}</p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        // Models with fixed pricing
                        <div className="text-center mt-12">
                          <div className="text-3xl font-bold text-[var(--primary-blue)] mb-2 items-center">
                            {model.cost}
                          </div>
                          <p className="text-xs text-gray-500">{model.pricing}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Pricing Examples Section */}
              <div className="mb-8">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
                    {t('pricing.examples.title')} <span className="text-gradient">{t('pricing.examples.titleHighlight')}</span>
                  </h2>
                  <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                    {t('pricing.examples.subtitle')}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
                  {/* Image Examples */}
                  <div className="glass-card rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Image className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                      <h3 className="text-lg font-semibold text-white">{t('pricing.examples.categories.singleImage.title')}</h3>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div className="text-gray-300">{t('pricing.examples.categories.singleImage.items.fluxDev')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.singleImage.items.seedream3')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.singleImage.items.imagen4Ultra')}</div>
                    </div>
                  </div>

                  <div className="glass-card rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Image className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                      <h3 className="text-lg font-semibold text-white">{t('pricing.examples.categories.batchGeneration.title')}</h3>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div className="text-gray-300">{t('pricing.examples.categories.batchGeneration.items.fluxDev')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.batchGeneration.items.seedream3')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.batchGeneration.items.imagen4Ultra')}</div>
                    </div>
                  </div>

                  {/* Video Examples */}
                  <div className="glass-card rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                      <h3 className="text-lg font-semibold text-white">{t('pricing.examples.categories.shortVideos.title')}</h3>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div className="text-gray-300">{t('pricing.examples.categories.shortVideos.items.seedanceLite480')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.shortVideos.items.seedancePro480')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.shortVideos.items.veo2')}</div>
                    </div>
                  </div>

                  <div className="glass-card rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                      <h3 className="text-lg font-semibold text-white">{t('pricing.examples.categories.hdVideos.title')}</h3>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div className="text-gray-300">{t('pricing.examples.categories.hdVideos.items.seedanceLite1080')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.hdVideos.items.seedancePro1080')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.hdVideos.items.hunyuan')}</div>
                    </div>
                  </div>

                  <div className="glass-card rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Video className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                      <h3 className="text-lg font-semibold text-white">{t('pricing.examples.categories.premiumVideos.title')}</h3>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div className="text-gray-300">{t('pricing.examples.categories.premiumVideos.items.veo3')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.premiumVideos.items.veo2Long')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.premiumVideos.items.seedanceProLong')}</div>
                    </div>
                  </div>

                  <div className="glass-card rounded-xl p-6">
                    <div className="flex items-center mb-4">
                      <Coins className="w-5 h-5 text-[var(--primary-blue)] mr-2" />
                      <h3 className="text-lg font-semibold text-white">{t('pricing.examples.categories.costComparison.title')}</h3>
                    </div>
                    <div className="space-y-3 text-sm">
                      <div className=" text-[var(--primary-blue)] mb-2">{t('pricing.examples.categories.costComparison.subtitle')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.costComparison.items.fluxDevImages')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.costComparison.items.seedanceLiteVideos')}</div>
                      <div className="text-gray-300">{t('pricing.examples.categories.costComparison.items.hunyuanVideos')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
        </section>
    );
};

export default Pricing;
