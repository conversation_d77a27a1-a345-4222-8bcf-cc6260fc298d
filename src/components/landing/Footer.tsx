import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';

const Footer = () => {
  const { t } = useTranslation();

  const footerLinks = {
    Legal: [
      { name: t('footer.sections.legal.privacyPolicy'), href: '/privacy-policy', isRoute: true },
      { name: t('footer.sections.legal.termsOfService'), href: '/terms-of-use', isRoute: true },
    ]
  };

  return (
    <footer className="relative bg-[var(--dark-secondary)]">
      <div className="container mx-auto px-4 pb-8">
        {/* Bottom Bar */}
        <div className=" mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm mb-4 md:mb-0">
              {t('footer.copyright')}
            </p>

            {/* Legal Links */}
            <div className="flex flex-wrap gap-6">
              {Object.entries(footerLinks).map(([category, links]) => (
                <div key={category} className="flex gap-4">
                  {links.map((link) => (
                    link.isRoute ? (
                      <Link
                        key={link.name}
                        to={link.href}
                        className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                      >
                        {link.name}
                      </Link>
                    ) : (
                      <a
                        key={link.name}
                        href={link.href}
                        className="text-gray-400 hover:text-white transition-colors duration-200 text-sm"
                      >
                        {link.name}
                      </a>
                    )
                  ))}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
