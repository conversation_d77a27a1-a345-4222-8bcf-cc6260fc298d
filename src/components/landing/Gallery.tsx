import { useState } from 'react';
import { Play, Eye } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const Gallery = () => {
  const [activeTab, setActiveTab] = useState('images');
  const { t } = useTranslation();

  const sampleImages = [
    {
      source: "seedream3-sample.jpg",
      prompt: t('gallery.sampleImages.0.prompt'),
      model: t('gallery.sampleImages.0.model')
    },
    {
      source: "imagen4-ultra-sample.png",
      prompt: t('gallery.sampleImages.1.prompt'),
      model: t('gallery.sampleImages.1.model')
    },
    {
      source: "flux-kontext-pro-sample.jpg",
      prompt: t('gallery.sampleImages.2.prompt'),
      model: t('gallery.sampleImages.2.model')
    },
    {
      source: "flux-dev-sample.webp",
      prompt: t('gallery.sampleImages.3.prompt'),
      model: t('gallery.sampleImages.3.model')
    },
  ];

  const sampleVideos = [
    {
      thumbnail: "https://images.unsplash.com/photo-1634017839464-5c339ebe3cb4?w=400&h=300&fit=crop",
      duration: "0:15",
      prompt: t('gallery.sampleVideos.0.prompt'),
      model: t('gallery.sampleVideos.0.model'),
      source: "veo3-sample.mp4"
    },
    {
      thumbnail: "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?w=400&h=300&fit=crop",
      duration: "0:30",
      prompt: t('gallery.sampleVideos.1.prompt'),
      model: t('gallery.sampleVideos.1.model'),
      source: "seedance-pro-sample.mp4"
    },
    {
      thumbnail: "https://images.unsplash.com/photo-1634017839481-9f4cf4f8fb67?w=400&h=300&fit=crop",
      duration: "0:12",
      prompt: t('gallery.sampleVideos.2.prompt'),
      model: t('gallery.sampleVideos.2.model'),
      source: "seedance-lite-sample.mp4"
    },
    {
      thumbnail: "https://images.unsplash.com/photo-1634017839492-24c2e5e5e1b8?w=400&h=300&fit=crop",
      duration: "0:25",
      prompt: t('gallery.sampleVideos.3.prompt'),
      model: t('gallery.sampleVideos.3.model'),
      source: "hunyuan-sample.mp4"
    }
  ];

  return (
    <section id="gallery" className="py-10 md:py-20 relative">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-white">
            <span className="text-gradient">{t('gallery.title')}</span> {t('gallery.titleHighlight')}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            {t('gallery.subtitle')}
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-12">
          <div className="glass-card rounded-full p-1 flex">
            <button
              onClick={() => setActiveTab('images')}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                activeTab === 'images'
                  ? 'bg-[var(--primary-blue)] text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              {t('gallery.tabs.images')}
            </button>
            <button
              onClick={() => setActiveTab('videos')}
              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                activeTab === 'videos'
                  ? 'bg-[var(--primary-blue)] text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              {t('gallery.tabs.videos')}
            </button>
          </div>
        </div>

        {/* Images Gallery */}
        {activeTab === 'images' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {sampleImages.map((image, index) => (
              <div
                key={index}
                className="group animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="relative cursor-pointer rounded-xl overflow-hidden mb-4">
                  <img
                    src={image.source}
                    alt={`${t('gallery.imageAlt')} ${index + 1}`}
                    className="w-full h-64 md:h-80 object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                </div>
                <div className="text-center mb-4">
                  <h3 className="text-xl font-bold text-gradient-blue mb-2">
                    {image.model}
                  </h3>
                  <p className="text-sm text-gray-300 leading-relaxed">"{image.prompt}"</p>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Videos Gallery */}
        {activeTab === 'videos' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {sampleVideos.map((video, index) => (
              <div
                key={index}
                className="group"
              >
                <div className="relative cursor-pointer rounded-xl overflow-hidden aspect-video mb-4">
                  <video
                    src={video.source}
                    className="w-full h-full object-cover"
                    controls
                    preload="metadata"
                    muted
                  >
                    {t('studio.statusMessages.browserNotSupported')}
                  </video>
                  <div className="absolute bottom-4 right-4 bg-black/60 text-white text-sm px-2 py-1 rounded">
                    {video.duration}
                  </div>
                </div>
                <div className="text-center mb-4">
                  <h3 className="text-xl font-bold text-gradient-blue mb-2">
                    {video.model}
                  </h3>
                  <p className="text-sm text-gray-300 leading-relaxed">"{video.prompt}"</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default Gallery;
