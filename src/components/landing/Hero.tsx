import { ArrowRight, Zap } from 'lucide-react';
import LaptopMockup from './LaptopMockup';
import {useNavigate} from "react-router-dom";
import { useTranslation } from 'react-i18next';

const Hero = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleExamplesClick = () => {
    const gallerySection = document.getElementById('gallery');
    if (gallerySection) {
      gallerySection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }

  const handleStartCreatingClick = () => {
    navigate('/studio');
  }

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-[var(--dark-primary)] via-[var(--dark-secondary)] to-blue-900/80">
        {/* Animated background shapes */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-[var(--primary-blue)]/10 rounded-full blur-xl animate-float"></div>
        <div className="absolute top-40 right-20 w-48 h-48 bg-[var(--accent-purple)]/10 rounded-full blur-xl animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-[var(--accent-cyan)]/10 rounded-full blur-xl animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      <div className="container mx-auto px-4 mt-20 relative z-10">
        <div className="text-center max-w-5xl mx-auto">
          {/* Main Heading */}
          <h1 className="text-white text-5xl md:text-7xl lg:text-8xl font-bold mb-6 animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
            {t('hero.title')} <span className="text-gradient">{t('hero.titleHighlight')}</span><br />
            {t('hero.titleSuffix')}
          </h1>

          {/* Subheading */}
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            {t('hero.subtitle')}
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12 animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <button
                onClick={handleStartCreatingClick}
                className="group px-8 py-4 bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white font-semibold rounded-xl transition-all duration-300 flex items-center gap-2 animate-glow"
            >
              {t('hero.startCreating')}
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </button>

            <button
                onClick={handleExamplesClick}
                className="px-8 py-4 glass-card hover:bg-white/10 text-white font-semibold rounded-xl transition-all duration-300 flex items-center gap-2"
            >
              <Zap className="w-5 h-5" />
              {t('hero.viewExamples')}
            </button>
          </div>

          {/* Stats */}
          <div className="flex flex-col sm:flex-row justify-center items-center gap-8 text-center mb-16 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
            <div>
              <div className="text-3xl font-bold text-white mb-1">{t('hero.stats.value1')}</div>
              <div className="text-gray-400">{t('hero.stats.label1')}</div>
            </div>
            <div className="hidden sm:block w-px h-12 bg-gray-600"></div>
            <div>
              <div className="text-3xl font-bold text-white mb-1">{t('hero.stats.value2')}</div>
              <div className="text-gray-400">{t('hero.stats.label2')}</div>
            </div>
            <div className="hidden sm:block w-px h-12 bg-gray-600"></div>
            <div>
              <div className="text-3xl font-bold text-white mb-1">{t('hero.stats.value3')}</div>
              <div className="text-gray-400">{t('hero.stats.label3')}</div>
            </div>
          </div>

          {/* Laptop Mockup */}
          <LaptopMockup />
        </div>
      </div>
    </section>
  );
};

export default Hero;
