import { Sparkles, Zap, Palette, Download, Shield, Cpu } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const Features = () => {
  const { t } = useTranslation();

  const features = [
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: t('features.items.aiPowered.title'),
      description: t('features.items.aiPowered.description')
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: t('features.items.lightningFast.title'),
      description: t('features.items.lightningFast.description')
    },
    {
      icon: <Palette className="w-8 h-8" />,
      title: t('features.items.artisticControl.title'),
      description: t('features.items.artisticControl.description')
    },
    {
      icon: <Download className="w-8 h-8" />,
      title: t('features.items.multipleFormats.title'),
      description: t('features.items.multipleFormats.description')
    },
    {
      icon: <Shield className="w-8 h-8" />,
      title: t('features.items.commercialLicense.title'),
      description: t('features.items.commercialLicense.description')
    },
    {
      icon: <Cpu className="w-8 h-8" />,
      title: t('features.items.advancedModels.title'),
      description: t('features.items.advancedModels.description')
    }
  ];

  return (
    <section id="features" className="py-10 md:py-20 relative z-10 bg-[var(--dark-primary)]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-4 text-white">
            {t('features.title')}{' '}
            <span className="text-gradient">{t('features.titleHighlight')}</span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            {t('features.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="items-center flex flex-col text-center glass-card rounded-2xl p-8 hover:scale-105 transition-all duration-300 hover:shadow-2xl hover:shadow-blue-500/10 animate-fade-in-up "
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="gradient-primary w-16 h-16 rounded-xl flex items-center justify-center mb-6 text-white">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-4 text-white">{feature.title}</h3>
              <p className="text-gray-300 leading-relaxed">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Features;
