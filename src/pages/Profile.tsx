import React, {useEffect, useState} from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { User, CreditCard, Download, Eye, CheckCircle, X } from 'lucide-react';
import SharedLayout from '@/components/SharedLayout';
import {usePBContext} from "@/context/PocketbaseContext.tsx";
import {useNavigate, useSearchParams} from "react-router-dom";
import { useTranslation } from 'react-i18next';
import {useMutation, useQuery} from "@tanstack/react-query";
import {api, mutationKeys, queryKeys} from "@/lib/api.ts";

const Profile = () => {
  const { user, pb, setUser } = usePBContext();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const [showCongratulations, setShowCongratulations] = useState(false);
  const [confettiPieces, setConfettiPieces] = useState<Array<{id: number, left: number, delay: number, color: string}>>([]);

  const handleLogout = () => {
    pb.authStore.clear();
    setUser(undefined);
    navigate('/');
  }

  const {data: userBalance } = useQuery({
    queryKey: queryKeys.userTokenBalance(user?.id || ''),
    queryFn: async () => {
      if (!user?.id) {
        throw new Error('No user ID found');
      }

      return await api.getUserTokenBalance(pb, user.id);
    },
    enabled: !!user?.id
  });

  // Handle successful payment redirect
  useEffect(() => {
    const topUpAmount = searchParams.get('topUp');
    if (topUpAmount) {
      // Generate confetti pieces
      const pieces = Array.from({ length: 20 }, (_, i) => ({
        id: i,
        left: Math.random() * 100,
        delay: Math.random() * 2,
        color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][Math.floor(Math.random() * 5)]
      }));
      setConfettiPieces(pieces);
      setShowCongratulations(true);
    }
  }, [searchParams]);

  const handleCloseCongratulations = () => {
    setShowCongratulations(false);
    setConfettiPieces([]);
    // Remove the query parameter from URL without triggering navigation
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.delete('topUp');
    setSearchParams(newSearchParams, { replace: true });
  };

  return (
      <SharedLayout
          title={t('profile.title')}
          description={t('profile.description')}
      >
        {/* Congratulations Modal */}
        {showCongratulations && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
            {/* Confetti */}
            {confettiPieces.map((piece) => (
              <div
                key={piece.id}
                className="absolute w-3 h-3 animate-confetti"
                style={{
                  left: `${piece.left}%`,
                  backgroundColor: piece.color,
                  // animationDelay: `${piece.delay}s`,
                  borderRadius: Math.random() > 0.5 ? '50%' : '0%'
                }}
              />
            ))}

            {/* Modal Content */}
            <div className="relative bg-white rounded-2xl p-8 max-w-md mx-4 text-center animate-bounce-in shadow-2xl">
              <button
                onClick={handleCloseCongratulations}
                className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>

              <div className="mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse-success">
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  {t('profile.congratulations.title')}
                </h2>
                <p className="text-gray-600 mb-2">
                  {t('profile.congratulations.message', { amount: searchParams.get('topUp') || '0' })}
                </p>
                <p className="text-sm text-gray-500">
                  {t('profile.congratulations.subtitle')}
                </p>
              </div>

              <Button
                onClick={handleCloseCongratulations}
                className="w-full bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white"
              >
                {t('profile.congratulations.continueButton')}
              </Button>
            </div>
          </div>
        )}
        <div className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-stretch">
            {/* Profile Information */}
            <div className="lg:col-span-2 flex">
              <Card className="border border-gray-200 shadow-sm flex-1">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="w-5 h-5" />
                    <span>{t('profile.sections.profileInformation')}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t('profile.fields.fullName')}</label>
                      <Input
                          value={user.name}
                          className="border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          disabled={true}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">{t('profile.fields.email')}</label>
                      <Input
                          type="email"
                          value={user.email}
                          className="border-gray-200 focus:border-purple-500 focus:ring-purple-500"
                          disabled={true}
                      />
                    </div>
                  </div>
                  <Button
                      onClick={handleLogout}
                      className="w-full bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white"
                  >
                    {t('profile.buttons.logout')}
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Account Stats */}
            <div className="flex">
              <Card className="border border-gray-200 shadow-sm flex-1">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <CreditCard className="w-5 h-5" />
                    <span>{t('profile.sections.credits')}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {userBalance}
                    </div>
                    <p className="text-gray-600 mb-4">{t('profile.stats.creditsRemaining')}</p>
                    <Button onClick={() => {
                      navigate('/pricing');
                      // createPayment({amount: '300'})
                    }} className="w-full bg-[var(--primary-blue)] hover:bg-[var(--primary-blue-hover)] text-white">
                      {t('profile.buttons.topup')}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </SharedLayout>
  );
};

export default Profile;
