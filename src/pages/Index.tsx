import Header from '@/components/landing/Header';
import Hero from '@/components/landing/Hero';
import Features from '@/components/landing/Features';
import Gallery from '@/components/landing/Gallery';
import FAQ from '@/components/landing/FAQ';
import Footer from '@/components/landing/Footer';
import Pricing from "@/components/landing/Pricing.tsx";
import {useReferral} from "@/hooks/use-referral.tsx";

const Index = () => {
  // import for effects
  const _ = useReferral();
  return (
    <div className="min-h-screen bg-[var(--dark-primary)]">
      <Header/>
      <Hero/>
      <Features/>
      <Gallery/>
      <Pricing/>
      <FAQ/>
      <Footer/>
    </div>
  );
};

export default Index;
