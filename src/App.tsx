import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient } from "@tanstack/react-query";
import {BrowserRouter, Routes, Route, useSearchParams} from "react-router-dom";
import "@/i18n/config";
import Index from './pages/Index.tsx'
import Studio from "./pages/Studio.tsx";
import Login from "./pages/Login";
import Profile from "./pages/Profile";
import Pricing from "./pages/Pricing";
import GenerationHistory from "./pages/GenerationHistory";
import NotFound from "./pages/NotFound";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import TermsOfUse from "./pages/TermsOfUse";
import ProtectedRoute from "@/components/ProtectedRoute.tsx";
import {PersistQueryClientProvider} from "@tanstack/react-query-persist-client";
import {createSyncStoragePersister} from "@tanstack/query-sync-storage-persister";
import {useEffect} from "react";

const App = () => {
  const debug = import.meta.env.DEV;

  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        gcTime: debug ? 50 : 1000 * 5, // 50 ms or 5 minutes
      },
    },
  })
  const persister = createSyncStoragePersister({
    storage: window.localStorage,
  })
  return (

    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{persister}}
    >
      <TooltipProvider>
        <Toaster/>
        <Sonner/>
        <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index/>}/>
              <Route path="/login" element={<Login/>}/>
              <Route path="/privacy-policy" element={<PrivacyPolicy/>}/>
              <Route path="/terms-of-use" element={<TermsOfUse/>}/>

              <Route element={<ProtectedRoute/>}>
                <Route path="/studio" element={<Studio/>}/>
                <Route path="/history" element={<GenerationHistory/>}/>
                <Route path="/profile" element={<Profile/>}/>
                <Route path="/pricing" element={<Pricing/>}/>
              </Route>

              <Route path="*" element={<NotFound/>}/>
            </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </PersistQueryClientProvider>
  );
};

export default App;
