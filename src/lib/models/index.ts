// Central export and mapping system for model configurations

import { ModelInputConfig } from './types';
// import { fluxDevConfig } from './flux-dev';
import { seedream3Config } from './seedream-3';
import { imagen4UltraConfig } from './imagen-4-ultra';
import { fluxKontextProConfig } from './flux-kontext-pro';
import { hunyuanConfig } from './hunyuan';
import { veo3Config } from './veo3';
import { veo3FastConfig } from './veo3-fast';
import { veo2Config } from './veo2';
import { seedanceProConfig } from './seedance-pro';
import { seedanceLiteConfig } from './seedance-lite';

// Export types
export * from './types';

// Model configurations registry
export const modelConfigs: Record<string, ModelInputConfig> = {
  // Image models
  // 'flux-dev': fluxDevConfig,
  'seedream-3': seedream3Config,
  'imagen-4-ultra': imagen4UltraConfig,
  'flux-kontext-pro': fluxKontextProConfig,
  // Video models
  'hunyuan': hunyuanConfig,
  'seedance-pro': seedanceProConfig,
  'seedance-lite': seedanceLiteConfig,
  'veo3': veo3Config,
  'veo3-fast': veo3FastConfig,
  'veo2': veo2Config,
};

/**
 * Get model configuration by model ID
 */
export function getModelConfig(modelId: string): ModelInputConfig | undefined {
  return modelConfigs[modelId];
}

/**
 * Get all available model configurations
 */
export function getAllModelConfigs(): ModelInputConfig[] {
  return Object.values(modelConfigs);
}

/**
 * Get model configurations by type (image or video)
 */
export function getModelConfigsByType(type: 'image' | 'video'): ModelInputConfig[] {
  return Object.values(modelConfigs).filter(config => config.metadata?.type === type);
}

/**
 * Check if a model has a specific parameter
 */
export function hasParameter(modelId: string, parameterName: string): boolean {
  const config = getModelConfig(modelId);
  return config ? parameterName in config.parameters : false;
}

/**
 * Get parameter configuration for a specific model and parameter
 */
export function getParameterConfig(modelId: string, parameterName: string) {
  const config = getModelConfig(modelId);
  return config?.parameters[parameterName];
}

/**
 * Get parameters grouped by their group property
 */
export function getParametersByGroup(modelId: string): Record<string, any[]> {
  const config = getModelConfig(modelId);
  if (!config) return {};

  const grouped: Record<string, any[]> = {};

  Object.entries(config.parameters).forEach(([paramName, paramConfig]) => {
    const groupName = paramConfig.group || 'default';
    if (!grouped[groupName]) {
      grouped[groupName] = [];
    }
    grouped[groupName].push({
      name: paramName,
      config: paramConfig
    });
  });

  // Sort parameters within each group by order
  Object.keys(grouped).forEach(groupName => {
    grouped[groupName].sort((a, b) => (a.config.order || 0) - (b.config.order || 0));
  });

  return grouped;
}

/**
 * Get group configurations for a model, sorted by order
 */
export function getGroupConfigs(modelId: string) {
  const config = getModelConfig(modelId);
  if (!config?.groups) return [];

  return Object.values(config.groups).sort((a, b) => (a.order || 0) - (b.order || 0));
}

/**
 * Get default values for all parameters of a model
 */
export function getDefaultValues(modelId: string): Record<string, any> {
  const config = getModelConfig(modelId);
  if (!config) return {};

  const defaults: Record<string, any> = {};

  Object.entries(config.parameters).forEach(([paramName, paramConfig]) => {
    if (paramConfig.defaultValue !== undefined) {
      defaults[paramName] = paramConfig.defaultValue;
    }
  });

  return defaults;
}

/**
 * Validate parameter value against its configuration
 */
export function validateParameter(modelId: string, parameterName: string, value: any): boolean {
  const paramConfig = getParameterConfig(modelId, parameterName);
  if (!paramConfig) return false;

  // Check required parameters
  if (paramConfig.required && (value === undefined || value === null || value === '')) {
    return false;
  }

  // Type-specific validation
  switch (paramConfig.type) {
    case 'number':
    case 'slider':
      if (typeof value !== 'number') return false;
      if (paramConfig.min !== undefined && value < paramConfig.min) return false;
      if (paramConfig.max !== undefined && value > paramConfig.max) return false;
      break;

    case 'select':
      if (paramConfig.options) {
        const validValues = paramConfig.options.map(opt => opt.value);
        if (!validValues.includes(value)) return false;
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean') return false;
      break;
  }

  return true;
}

/**
 * Get required parameters for a model
 */
export function getRequiredParameters(modelId: string): string[] {
  const config = getModelConfig(modelId);
  if (!config) return [];

  return Object.entries(config.parameters)
    .filter(([_, paramConfig]) => paramConfig.required)
    .map(([paramName, _]) => paramName);
}

/**
 * Check if all required parameters are provided
 */
export function validateRequiredParameters(modelId: string, values: Record<string, any>): boolean {
  const requiredParams = getRequiredParameters(modelId);
  return requiredParams.every(param =>
    values[param] !== undefined &&
    values[param] !== null &&
    values[param] !== ''
  );
}
