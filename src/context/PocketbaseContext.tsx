import React, {create<PERSON>ontext, PropsWith<PERSON>hildren, useContext, useEffect, useMemo, useState} from 'react';
import PocketBase, {UnsubscribeFunc} from "pocketbase";

const {
  VITE_BACKEND_URL: BACKEND_URL,
} = import.meta.env

export interface User {
  id: string;
  created: string;
  updated: string;
  name: string;
  email: string;
  emailVisibility: boolean;
  avatar: string;
  tokens: number;
}

export interface IPocketbaseContext {
  pb: PocketBase;
  user?: User;
  setUser: (user: User | undefined) => void;
}

const PocketbaseContext = createContext<IPocketbaseContext | undefined>(undefined);

export const usePBContext = () => {
  const context = useContext(PocketbaseContext);
  if (!context) {
    throw new Error('usePBContext must be used within a PocketbaseProvider');
  }
  return context;
};

export const PocketbaseProvider: React.FC<PropsWithChildren> = ({children}) => {


  const pb = useMemo(() => {
    console.log(`creating new pb instance for ${BACKEND_URL}`)
    const p = new PocketBase(BACKEND_URL);
    p.autoCancellation(false);
    p.collection('users').authRefresh().then((authResp)=>{
      console.log('refreshed auth', authResp)
      setUser(authResp.record as unknown as User);
    })
    return p;
  }, [BACKEND_URL]);

  const [user, setUser] = useState<User | undefined>(pb.authStore.record as unknown as User);

  useEffect(() => {
    if (user) {
      try {
        //@ts-ignore
        window.Happydesk.setUserAccount(user.email, user.name);
      } catch (e) {
        console.log("error while calling happydesk", e)
      }
    }
  }, [user]);

  let userUnsub: UnsubscribeFunc | undefined = undefined
  useEffect(() => {
    if (pb.authStore.isValid) {
      if (userUnsub) {
        console.debug("unsubscribe beginning of user updates")
        userUnsub()
      }
      const model = pb.authStore.record as any | null;
      if (model) {
        pb.collection("users").subscribe(model.id, ({action, record}) => {
          console.debug(`received realtime user action - '${action}'`, record)

          if (action == "update") {
            console.debug('Received user update, setting user')
            setUser(record as unknown as User)
          }
        }, {}).then((f) => {
          userUnsub = f
        }).catch((err) => {
          console.error("Error while subscribing to user", err)
        })
      }
      return () => {
        if (userUnsub) {
          console.debug("unsubscribe from user updates")
          userUnsub()
        }
      }
    }
  }, [pb.authStore.isValid]);


  return (
    <PocketbaseContext.Provider value={{pb, user, setUser}}>
      {children}
    </PocketbaseContext.Provider>
  );
};
