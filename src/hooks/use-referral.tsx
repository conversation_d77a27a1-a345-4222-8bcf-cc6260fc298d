import * as React from "react"
import {useSearchParams} from "react-router-dom";

export function useReferral() {

  const [searchParams, setSearchParams] = useSearchParams();

  const [referral, setReferral] = React.useState<string | undefined>(localStorage.getItem('referral'))

  React.useEffect(() => {
    const referral = searchParams.get('referral');
    if (referral) {
      localStorage.setItem('referral', referral);
      setReferral(referral);
      console.log("Saved referral id ", referral)
      // Remove the query parameter from URL without triggering navigation
      const newSearchParams = new URLSearchParams(searchParams);
      newSearchParams.delete('referral');
      setSearchParams(newSearchParams, {replace: true});
    }
  }, [searchParams]);
  return referral
}

