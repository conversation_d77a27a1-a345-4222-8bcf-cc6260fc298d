#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color


# Error handling
set -e
trap 'echo -e "${RED}Error: Build and deploy failed${NC}"; exit 1' ERR


#rm -rf dist/
#VITE_BACKEND_URL="https://api.holstai.ru/" npm run build
#rsync -avhz -O --no-perms -e 'ssh -p 22' --delete ./dist/ holst@79.174.81.223:/var/www/holstai.ru/

ssh holst@79.174.81.223 \
"sudo chown -R caddy:caddy /var/www/holstai.ru/"
